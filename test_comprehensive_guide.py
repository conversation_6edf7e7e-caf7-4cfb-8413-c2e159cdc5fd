#!/usr/bin/env python3
"""
Comprehensive test script for the guide editor functionality.
Tests both text mode and rich mode, as well as nested numbering preservation.
"""

import requests
import time
import json

BASE_URL = "http://127.0.0.1:5555"

def authenticate_session():
    """Authenticate with the application."""
    session = requests.Session()
    login_data = {"password": "uproar321"}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200 and "login" not in response.url.lower():
        print("✓ Authentication successful")
        return session
    else:
        print("✗ Authentication failed")
        return None

def test_text_mode_nested_numbering(session):
    """Test nested numbering in text mode."""
    print("\n--- Testing Text Mode Nested Numbering ---")
    
    test_content = """1 Main step one
1.1 Sub-step one
1.1.1 Sub-sub-step one
1.1.2 Sub-sub-step two
1.2 Sub-step two
2 Main step two
2.1 Sub-step under main two
2.1.1 Deep nested step
2.1.2 Another deep nested step
2.2 Another sub-step
3 Main step three"""

    # Save content as if it came from text mode (with <br> tags)
    content_data = {
        "tab_id": 1,
        "content": test_content.replace('\n', '<br>')
    }
    
    response = session.post(f"{BASE_URL}/api/guide/content",
                           json=content_data,
                           headers={"Content-Type": "application/json"})
    
    if response.status_code == 200:
        print("✓ Text mode content saved successfully")
        
        # Verify content preservation
        time.sleep(1)
        response = session.get(f"{BASE_URL}/guide?tab=1")
        content = response.text
        
        nested_patterns = ["1.1.1", "1.1.2", "2.1.1", "2.1.2"]
        all_found = all(pattern in content for pattern in nested_patterns)
        
        if all_found:
            print("✓ All nested numbering patterns preserved in text mode")
            return True
        else:
            print("✗ Some nested numbering patterns lost in text mode")
            return False
    else:
        print(f"✗ Failed to save text mode content: {response.status_code}")
        return False

def test_rich_mode_formatting(session):
    """Test rich mode formatting capabilities."""
    print("\n--- Testing Rich Mode Formatting ---")
    
    # Rich content with HTML formatting
    rich_content = """<h2>Rich Mode Test</h2>
<p><strong>Bold text</strong> and <em>italic text</em></p>
<ul>
<li>Bullet point one</li>
<li class="ql-indent-1">Indented bullet point</li>
<li class="ql-indent-2">Double indented bullet point</li>
<li>Regular bullet point</li>
</ul>
<p>Regular paragraph with <span style="color: rgb(230, 0, 0);">red text</span></p>"""

    content_data = {
        "tab_id": 2,  # Use a different tab
        "content": rich_content
    }
    
    response = session.post(f"{BASE_URL}/api/guide/content",
                           json=content_data,
                           headers={"Content-Type": "application/json"})
    
    if response.status_code == 200:
        print("✓ Rich mode content saved successfully")
        
        # Verify content preservation
        time.sleep(1)
        response = session.get(f"{BASE_URL}/guide?tab=2")
        content = response.text
        
        rich_elements = ["<h2>", "<strong>", "<em>", "ql-indent-1", "ql-indent-2", "color: rgb(230, 0, 0)"]
        found_elements = [element for element in rich_elements if element in content]
        
        if len(found_elements) >= 4:  # Most elements should be preserved
            print(f"✓ Rich formatting preserved ({len(found_elements)}/{len(rich_elements)} elements found)")
            return True
        else:
            print(f"✗ Rich formatting not well preserved ({len(found_elements)}/{len(rich_elements)} elements found)")
            return False
    else:
        print(f"✗ Failed to save rich mode content: {response.status_code}")
        return False

def test_mixed_content(session):
    """Test content that mixes text and rich formatting."""
    print("\n--- Testing Mixed Content ---")
    
    mixed_content = """<h2>Mixed Content Test</h2>
<p>1 First main step</p>
<p>1.1 First sub-step</p>
<p>1.1.1 First sub-sub-step</p>
<p><strong>2 Second main step (bold)</strong></p>
<p>2.1 Second sub-step</p>
<ul>
<li>Regular bullet</li>
<li class="ql-indent-1">Indented bullet</li>
</ul>
<p>3 Third main step</p>
<p>3.1 Third sub-step with <em>italic text</em></p>"""

    content_data = {
        "tab_id": 1,  # Overwrite tab 1 with mixed content
        "content": mixed_content
    }
    
    response = session.post(f"{BASE_URL}/api/guide/content",
                           json=content_data,
                           headers={"Content-Type": "application/json"})
    
    if response.status_code == 200:
        print("✓ Mixed content saved successfully")
        
        # Verify both numbering and formatting are preserved
        time.sleep(1)
        response = session.get(f"{BASE_URL}/guide?tab=1")
        content = response.text
        
        numbering_preserved = all(pattern in content for pattern in ["1.1.1", "2.1", "3.1"])
        formatting_preserved = all(element in content for element in ["<strong>", "<em>", "ql-indent-1"])
        
        if numbering_preserved and formatting_preserved:
            print("✓ Both nested numbering and rich formatting preserved")
            return True
        else:
            print(f"✗ Content not fully preserved (numbering: {numbering_preserved}, formatting: {formatting_preserved})")
            return False
    else:
        print(f"✗ Failed to save mixed content: {response.status_code}")
        return False

def main():
    """Run all tests."""
    print("Starting comprehensive guide editor tests...")
    
    session = authenticate_session()
    if not session:
        print("❌ Cannot proceed without authentication")
        return False
    
    tests = [
        test_text_mode_nested_numbering,
        test_rich_mode_formatting,
        test_mixed_content
    ]
    
    results = []
    for test in tests:
        try:
            result = test(session)
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with error: {e}")
            results.append(False)
    
    print(f"\n--- Test Summary ---")
    print(f"Tests passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed! The guide editor is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
