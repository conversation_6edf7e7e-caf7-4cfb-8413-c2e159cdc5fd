#!/usr/bin/env python3
"""
Test script to verify nested numbering functionality in the guide editor.
"""

import requests
import time
import json

BASE_URL = "http://127.0.0.1:5555"

def test_nested_numbering():
    """Test that nested numbering is preserved when saving and displaying content."""
    
    print("Testing nested numbering functionality...")
    
    # Test content with nested numbering
    test_content = """1 Main step one
1.1 Sub-step one
1.1.1 Sub-sub-step one
1.1.2 Sub-sub-step two
1.2 Sub-step two
2 Main step two
2.1 Sub-step under main two
2.1.1 Deep nested step
2.1.2 Another deep nested step
2.2 Another sub-step
3 Main step three"""

    session = requests.Session()

    # First, authenticate with the application
    try:
        # Login with the password
        login_data = {"password": "uproar321"}
        response = session.post(f"{BASE_URL}/login", data=login_data)

        if response.status_code == 200 and "login" not in response.url.lower():
            print("✓ Authentication successful")
        else:
            print("✗ Authentication failed")
            return False

        print("✓ Access to guide page successful")
        
        # Test saving content in text mode (which should preserve nested numbering)
        content_data = {
            "tab_id": 1,  # Assuming tab 1 exists
            "content": test_content.replace('\n', '<br>')  # Convert to HTML line breaks
        }
        
        print("Saving test content with nested numbering...")
        response = session.post(f"{BASE_URL}/api/guide/content",
                               json=content_data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if "message" in result:
                print("✓ Content saved successfully!")
                
                # Wait a moment and then fetch the page to verify
                time.sleep(1)
                response = session.get(f"{BASE_URL}/guide?tab=1")
                
                # Check if nested numbering patterns are preserved
                content = response.text
                
                checks = [
                    ("1.1.1" in content, "1.1.1 numbering preserved"),
                    ("1.1.2" in content, "1.1.2 numbering preserved"),
                    ("2.1.1" in content, "2.1.1 numbering preserved"),
                    ("2.1.2" in content, "2.1.2 numbering preserved")
                ]
                
                all_passed = True
                for check, description in checks:
                    if check:
                        print(f"✓ {description}")
                    else:
                        print(f"✗ {description}")
                        all_passed = False
                
                if all_passed:
                    print("\n🎉 All nested numbering tests passed!")
                    return True
                else:
                    print("\n❌ Some nested numbering tests failed")
                    return False
                    
            else:
                print(f"✗ Save failed: {result}")
                return False
        else:
            print(f"✗ Request failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the application. Make sure it's running on http://127.0.0.1:5555")
        return False
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_nested_numbering()
    if success:
        print("\nTest completed successfully! ✅")
    else:
        print("\nTest failed! ❌")
        print("Please check the application logs and try again.")
