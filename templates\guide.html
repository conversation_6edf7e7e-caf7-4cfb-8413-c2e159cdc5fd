{% extends "base.html" %}

{% block title %}11.2 Guide - Uproar{% endblock %}

{% block extra_css %}
<!-- Quill.js Rich Text Editor -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
.guide-container {
    display: flex;
    gap: 2rem;
    min-height: calc(100vh - 120px);
}

.vertical-tabs {
    min-width: 250px;
    max-width: 300px;
    background: var(--secondary-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
    height: fit-content;
    border: 1px solid var(--border-color);
}

.tab-item {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.tab-content {
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tab-link {
    color: var(--text-secondary);
    text-decoration: none;
    flex: 1;
    cursor: pointer;
}

.tab-item:hover {
    background: var(--tertiary-bg);
    transform: translateX(5px);
}

.tab-item:hover .tab-link {
    color: var(--text-primary);
    text-decoration: none;
}

.tab-item.active {
    background: var(--accent-red);
    border-color: var(--accent-red);
    box-shadow: var(--shadow);
}

.tab-item.active .tab-link {
    color: var(--text-primary);
}

.tab-item .tab-actions {
    float: right;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-item:hover .tab-actions {
    opacity: 1;
}

.content-area {
    flex: 1;
    background: var(--secondary-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    border: 1px solid var(--border-color);
    position: relative;
}

.content-display {
    min-height: 400px;
    line-height: 1.8;
    white-space: pre-wrap;
}

/* Preserve nested numbering formatting */
.content-display p {
    margin-bottom: 0.5rem;
}

.content-display ol {
    counter-reset: none;
}

.content-display ol li {
    list-style: none;
    position: relative;
}

/* Support for Quill.js indented lists */
.content-display .ql-indent-1 {
    margin-left: 2rem;
}

.content-display .ql-indent-2 {
    margin-left: 4rem;
}

.content-display .ql-indent-3 {
    margin-left: 6rem;
}

.content-display .ql-indent-4 {
    margin-left: 8rem;
}

.content-display .ql-indent-5 {
    margin-left: 10rem;
}

.content-display .ql-indent-6 {
    margin-left: 12rem;
}

.content-display .ql-indent-7 {
    margin-left: 14rem;
}

.content-display .ql-indent-8 {
    margin-left: 16rem;
}

/* Preserve custom numbering in plain text format */
.content-display br + br {
    display: block;
    margin: 0.5rem 0;
}

.content-editor {
    width: 100%;
    min-height: 400px;
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    color: var(--text-primary);
    font-family: 'Inter', sans-serif;
    resize: vertical;
}

.edit-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
}

.btn-edit {
    background: var(--info-color);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-edit:hover {
    background: #1a5fb4;
    transform: translateY(-1px);
}

.btn-save {
    background: var(--success-color);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-save:hover {
    background: #1e6f3e;
    transform: translateY(-1px);
}

.btn-cancel {
    background: var(--danger-color);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #b52d2a;
    transform: translateY(-1px);
}

.add-tab-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--success-color);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.add-tab-btn:hover {
    background: #1e6f3e;
    transform: translateY(-1px);
}

.tab-delete-btn {
    background: var(--danger-color);
    border: none;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    cursor: pointer;
    margin-left: 0.5rem;
}

.tab-delete-btn:hover {
    background: #b52d2a;
}

.tab-edit-btn {
    background: var(--info-color);
    border: none;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    cursor: pointer;
    margin-right: 0.25rem;
}

.tab-edit-btn:hover {
    background: #1a5fb4;
}

.tab-title-input {
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 3px;
    width: 100%;
    margin-bottom: 0.5rem;
}

.tab-title-input:focus {
    outline: none;
    border-color: var(--accent-red);
    box-shadow: 0 0 0 0.2rem rgba(196, 31, 59, 0.25);
}

.tab-edit-controls {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.tab-save-btn, .tab-cancel-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 3px;
    font-size: 0.75rem;
    cursor: pointer;
}

.tab-save-btn {
    background: var(--success-color);
    color: white;
}

.tab-save-btn:hover {
    background: #1e6f3e;
}

.tab-cancel-btn {
    background: var(--danger-color);
    color: white;
}

.tab-cancel-btn:hover {
    background: #b52d2a;
}

.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.alert-success {
    background: rgba(35, 134, 54, 0.2);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(218, 54, 51, 0.2);
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
}

.form-control {
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.form-control:focus {
    background: var(--tertiary-bg);
    border-color: var(--accent-red);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(196, 31, 59, 0.25);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* Quill Editor Styling */
.ql-editor {
    background: var(--tertiary-bg);
    color: var(--text-primary);
    border: none;
    min-height: 400px;
    font-family: 'Inter', sans-serif;
    line-height: 1.8;
}

.ql-toolbar {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.ql-container {
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    background: var(--tertiary-bg);
}

.ql-toolbar .ql-stroke {
    stroke: var(--text-secondary);
}

.ql-toolbar .ql-fill {
    fill: var(--text-secondary);
}

.ql-toolbar .ql-picker-label {
    color: var(--text-secondary);
}

.ql-toolbar button:hover .ql-stroke,
.ql-toolbar button.ql-active .ql-stroke {
    stroke: var(--accent-red);
}

.ql-toolbar button:hover .ql-fill,
.ql-toolbar button.ql-active .ql-fill {
    fill: var(--accent-red);
}

.ql-toolbar button:hover,
.ql-toolbar button.ql-active {
    background: var(--tertiary-bg);
}

.ql-picker-options {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
}

.ql-picker-item:hover {
    background: var(--tertiary-bg);
    color: var(--text-primary);
}

.text-editor-container {
    display: none;
    margin-top: 1rem;
}

.rich-editor-container {
    display: none;
    margin-top: 1rem;
}

.editor-controls {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-book-open text-accent me-2"></i>
                    11.2 Guide
                </h1>
            </div>
        </div>
    </div>
</div>

<!-- Alert Messages -->
<div id="alertContainer"></div>

<!-- Guide Content -->
<div class="guide-container">
    <!-- Vertical Tabs -->
    <div class="vertical-tabs">
        <h5 class="mb-3">
            <i class="fas fa-list me-2"></i>Topics
        </h5>
        
        {% for tab in tabs %}
        <div class="tab-item {% if tab.id == selected_tab_id %}active{% endif %}" data-tab-id="{{ tab.id }}">
            <div class="tab-content" id="tab-content-{{ tab.id }}">
                <a href="/guide?tab={{ tab.id }}" class="tab-link">
                    <span class="tab-title" id="tab-title-{{ tab.id }}">{{ tab.title }}</span>
                </a>
                {% if is_authenticated %}
                <span class="tab-actions">
                    <button class="tab-edit-btn" onclick="editTabTitle({{ tab.id }}); return false;">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="tab-delete-btn" onclick="deleteTab({{ tab.id }}); return false;">
                        <i class="fas fa-trash"></i>
                    </button>
                </span>
                {% endif %}
            </div>

            {% if is_authenticated %}
            <div class="tab-edit-form" id="tab-edit-{{ tab.id }}" style="display: none;">
                <input type="text" class="tab-title-input" id="tab-title-input-{{ tab.id }}" value="{{ tab.title }}">
                <div class="tab-edit-controls">
                    <button class="tab-save-btn" onclick="saveTabTitle({{ tab.id }})">
                        <i class="fas fa-save"></i> Save
                    </button>
                    <button class="tab-cancel-btn" onclick="cancelTabEdit({{ tab.id }})">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if is_authenticated %}
        <button class="add-tab-btn" onclick="addNewTab()">
            <i class="fas fa-plus me-2"></i>Add New Tab
        </button>
        {% endif %}
    </div>
    
    <!-- Content Area -->
    <div class="content-area">
        {% if is_authenticated %}
        <div class="edit-controls">
            <button class="btn-edit" id="editBtn" onclick="toggleEdit()">
                <i class="fas fa-edit me-1"></i>Edit
            </button>
            <button class="btn-edit" id="editTextBtn" onclick="toggleTextEdit()" style="display: none;" title="Switch to Text Mode for preserving exact numbering like 1.1.2">
                <i class="fas fa-align-left me-1"></i>Text Mode
            </button>
            <button class="btn-edit" id="editRichBtn" onclick="toggleRichEdit()" style="display: none;" title="Switch to Rich Mode for advanced formatting">
                <i class="fas fa-palette me-1"></i>Rich Mode
            </button>
            <button class="btn-save" id="saveBtn" onclick="saveContent()" style="display: none;">
                <i class="fas fa-save me-1"></i>Save
            </button>
            <button class="btn-cancel" id="cancelBtn" onclick="cancelEdit()" style="display: none;">
                <i class="fas fa-times me-1"></i>Cancel
            </button>
        </div>
        {% endif %}
        
        <div id="contentDisplay" class="content-display">
            {{ selected_content|safe or 'No content available for this tab.' }}
        </div>

        <div id="textEditorContainer" class="text-editor-container" style="display: none;">
            <textarea id="textEditor" class="content-editor" placeholder="Text Mode: Use this mode specifically to preserve exact numbering like 1.1.2, 2.3.1, etc. For rich formatting, switch to Rich Mode."></textarea>
            <div class="editor-controls">
                <button class="btn-save" onclick="saveTextContent()">
                    <i class="fas fa-save me-1"></i>Save
                </button>
                <button class="btn-cancel" onclick="cancelTextEdit()">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
            </div>
        </div>

        <div id="richEditorContainer" class="rich-editor-container" style="display: none;">
            <div id="richEditor"></div>
            <div class="editor-controls">
                <button class="btn-save" onclick="saveRichContent()">
                    <i class="fas fa-save me-1"></i>Save
                </button>
                <button class="btn-cancel" onclick="cancelRichEdit()">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Tab Modal -->
{% if is_authenticated %}
<div class="modal fade" id="addTabModal" tabindex="-1" aria-labelledby="addTabModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--secondary-bg); border: 1px solid var(--border-color);">
            <div class="modal-header">
                <h5 class="modal-title" id="addTabModalLabel">Add New Tab</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addTabForm">
                    <div class="mb-3">
                        <label for="tabTitle" class="form-label">Tab Title</label>
                        <input type="text" class="form-control" id="tabTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="tabOrder" class="form-label">Order</label>
                        <input type="number" class="form-control" id="tabOrder" value="{{ tabs|length + 1 }}" min="1">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveNewTab()">Add Tab</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quill.js Rich Text Editor -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

<script>
let isEditing = false;
let originalContent = '';
let quillEditor = null;
let editMode = 'text'; // 'text' or 'rich'

// Initialize Quill editor
document.addEventListener('DOMContentLoaded', function() {
    quillEditor = new Quill('#richEditor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                [{ 'font': [] }],
                [{ 'size': ['small', false, 'large', 'huge'] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'script': 'sub'}, { 'script': 'super' }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                [{ 'align': [] }],
                ['blockquote', 'code-block'],
                ['link', 'image', 'video'],
                ['clean']
            ]
        },
        placeholder: 'Enter your content here...',
        formats: ['header', 'font', 'size', 'bold', 'italic', 'underline', 'strike', 'color', 'background', 'script', 'list', 'bullet', 'indent', 'align', 'blockquote', 'code-block', 'link', 'image', 'video']
    });

    // Disable automatic list formatting to preserve custom numbering
    quillEditor.keyboard.addBinding({
        key: 'Enter',
        collapsed: true,
        format: ['list'],
        prefix: /^\d+\.\d*\.?\d*\s$/,
        handler: function(range, context) {
            // Don't auto-format numbered lists, just insert a new line
            this.quill.insertText(range.index, '\n');
            this.quill.setSelection(range.index + 1);
            return false;
        }
    });

    // Process content display to preserve nested numbering
    processContentDisplay();
});

function processContentDisplay() {
    const contentDisplay = document.getElementById('contentDisplay');
    if (!contentDisplay) return;

    // If content contains plain text with nested numbering, format it properly
    let content = contentDisplay.innerHTML.trim(); // Trim leading/trailing whitespace

    // Check if content looks like plain text with line breaks
    if (content.includes('<br>') && !content.includes('<p>') && !content.includes('<ul>')) {
        // Convert line breaks to proper paragraph structure for better display
        const lines = content.split('<br>').filter(line => line.trim().length > 0);
        const formattedContent = lines.map(line => {
            const trimmed = line.trim();
            if (trimmed.length === 0) return '';

            // Check for nested numbering pattern
            const numberMatch = trimmed.match(/^(\d+(?:\.\d+)*)\s+(.+)$/);
            if (numberMatch) {
                const number = numberMatch[1];
                const text = numberMatch[2];
                const depth = (number.match(/\./g) || []).length;
                const marginLeft = depth * 2; // 2rem per level
                return `<p style="margin-left: ${marginLeft}rem; margin-bottom: 0.5rem; margin-top: 0;"><strong>${number}</strong> ${text}</p>`;
            } else {
                return `<p style="margin-bottom: 0.5rem; margin-top: 0;">${trimmed}</p>`;
            }
        }).join('');

        if (formattedContent !== content) {
            contentDisplay.innerHTML = formattedContent;
        }
    } else {
        // Even for other content, ensure no leading whitespace
        if (content !== contentDisplay.innerHTML.trim()) {
            contentDisplay.innerHTML = content;
        }
    }
}

function toggleEdit() {
    // Default to rich mode as the standard editor
    toggleRichEdit();
}

function toggleTextEdit() {
    isEditing = true;
    editMode = 'text';
    originalContent = document.getElementById('contentDisplay').innerHTML;

    // Convert HTML to plain text for text editor, preserving line breaks and structure
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = originalContent;

    // Better conversion that preserves structure
    let plainText = convertHtmlToPlainText(tempDiv);

    document.getElementById('textEditor').value = plainText;

    document.getElementById('contentDisplay').style.display = 'none';
    document.getElementById('textEditorContainer').style.display = 'block';
    document.getElementById('editBtn').style.display = 'none';
    document.getElementById('editTextBtn').style.display = 'none';
    document.getElementById('editRichBtn').style.display = 'inline-block';
    document.getElementById('saveBtn').style.display = 'inline-block';
    document.getElementById('cancelBtn').style.display = 'inline-block';
}

function convertHtmlToPlainText(element) {
    let text = '';

    for (let node of element.childNodes) {
        if (node.nodeType === Node.TEXT_NODE) {
            text += node.textContent;
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const tagName = node.tagName.toLowerCase();

            if (tagName === 'br') {
                text += '\n';
            } else if (tagName === 'p') {
                if (text && !text.endsWith('\n')) {
                    text += '\n';
                }
                text += convertHtmlToPlainText(node);
                text += '\n';
            } else if (tagName === 'h1' || tagName === 'h2' || tagName === 'h3' ||
                      tagName === 'h4' || tagName === 'h5' || tagName === 'h6') {
                if (text && !text.endsWith('\n')) {
                    text += '\n';
                }
                text += convertHtmlToPlainText(node);
                text += '\n';
            } else if (tagName === 'ul' || tagName === 'ol') {
                text += convertHtmlToPlainText(node);
            } else if (tagName === 'li') {
                if (text && !text.endsWith('\n')) {
                    text += '\n';
                }
                text += convertHtmlToPlainText(node);
                text += '\n';
            } else {
                text += convertHtmlToPlainText(node);
            }
        }
    }

    return text;
}

function toggleRichEdit() {
    isEditing = true;
    editMode = 'rich';

    if (!originalContent) {
        originalContent = document.getElementById('contentDisplay').innerHTML;
    }

    // If switching from text mode, convert text to HTML first
    if (document.getElementById('textEditorContainer').style.display === 'block') {
        const textContent = document.getElementById('textEditor').value;
        const htmlContent = convertTextToHtml(textContent);
        quillEditor.root.innerHTML = htmlContent;
    } else {
        // Set the editor content from original HTML
        quillEditor.root.innerHTML = originalContent;
    }

    document.getElementById('contentDisplay').style.display = 'none';
    document.getElementById('textEditorContainer').style.display = 'none';
    document.getElementById('richEditorContainer').style.display = 'block';
    document.getElementById('editBtn').style.display = 'none';
    document.getElementById('editTextBtn').style.display = 'inline-block';
    document.getElementById('editRichBtn').style.display = 'none';
    document.getElementById('saveBtn').style.display = 'inline-block';
    document.getElementById('cancelBtn').style.display = 'inline-block';
}

function convertTextToHtml(text) {
    // Convert plain text to HTML, preserving line breaks and basic formatting
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .map(line => `<p>${line}</p>`)
        .join('');
}

function cancelEdit() {
    if (editMode === 'text') {
        cancelTextEdit();
    } else {
        cancelRichEdit();
    }
}

function cancelTextEdit() {
    isEditing = false;

    document.getElementById('contentDisplay').style.display = 'block';
    document.getElementById('textEditorContainer').style.display = 'none';
    document.getElementById('editBtn').style.display = 'inline-block';
    document.getElementById('editTextBtn').style.display = 'none';
    document.getElementById('editRichBtn').style.display = 'none';
    document.getElementById('saveBtn').style.display = 'none';
    document.getElementById('cancelBtn').style.display = 'none';

    // Reset editor content
    document.getElementById('textEditor').value = '';
}

function cancelRichEdit() {
    isEditing = false;

    document.getElementById('contentDisplay').style.display = 'block';
    document.getElementById('richEditorContainer').style.display = 'none';
    document.getElementById('editBtn').style.display = 'inline-block';
    document.getElementById('editTextBtn').style.display = 'none';
    document.getElementById('editRichBtn').style.display = 'none';
    document.getElementById('saveBtn').style.display = 'none';
    document.getElementById('cancelBtn').style.display = 'none';

    // Reset editor content
    quillEditor.root.innerHTML = originalContent;
}

function saveContent() {
    if (editMode === 'text') {
        saveTextContent();
    } else {
        saveRichContent();
    }
}

function saveTextContent() {
    const content = document.getElementById('textEditor').value;
    const tabId = {{ selected_tab_id }};

    // Convert plain text to HTML, preserving line breaks and formatting
    const htmlContent = content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/\n/g, '<br>')
        .replace(/  /g, '&nbsp;&nbsp;'); // Preserve multiple spaces

    fetch('/api/guide/content', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tab_id: tabId,
            content: htmlContent
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('Content saved successfully!', 'success');
            document.getElementById('contentDisplay').innerHTML = htmlContent;
            processContentDisplay(); // Process the content for proper nested numbering display
            cancelTextEdit();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Error saving content: ' + error, 'danger');
    });
}

function saveRichContent() {
    const content = quillEditor.root.innerHTML;
    const tabId = {{ selected_tab_id }};

    fetch('/api/guide/content', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tab_id: tabId,
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('Content saved successfully!', 'success');
            document.getElementById('contentDisplay').innerHTML = content;
            processContentDisplay(); // Process the content for proper display
            cancelRichEdit();
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Error saving content: ' + error, 'danger');
    });
}

function addNewTab() {
    const modal = new bootstrap.Modal(document.getElementById('addTabModal'));
    modal.show();
}

function saveNewTab() {
    const title = document.getElementById('tabTitle').value;
    const order = document.getElementById('tabOrder').value;
    
    if (!title) {
        showAlert('Tab title is required', 'danger');
        return;
    }
    
    fetch('/api/guide/tabs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            title: title,
            order: parseInt(order)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('Tab created successfully!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Error creating tab: ' + error, 'danger');
    });
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addTabModal'));
    modal.hide();
}

function editTabTitle(tabId) {
    // Hide the tab content and show the edit form
    document.getElementById(`tab-content-${tabId}`).style.display = 'none';
    document.getElementById(`tab-edit-${tabId}`).style.display = 'block';

    // Focus on the input field
    document.getElementById(`tab-title-input-${tabId}`).focus();
}

function cancelTabEdit(tabId) {
    // Show the tab content and hide the edit form
    document.getElementById(`tab-content-${tabId}`).style.display = 'flex';
    document.getElementById(`tab-edit-${tabId}`).style.display = 'none';

    // Reset the input value to original
    const originalTitle = document.getElementById(`tab-title-${tabId}`).textContent;
    document.getElementById(`tab-title-input-${tabId}`).value = originalTitle;
}

function saveTabTitle(tabId) {
    const newTitle = document.getElementById(`tab-title-input-${tabId}`).value.trim();

    if (!newTitle) {
        showAlert('Tab title cannot be empty', 'danger');
        return;
    }

    fetch('/api/guide/tabs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id: tabId,
            title: newTitle,
            order: parseInt(document.querySelector(`[data-tab-id="${tabId}"]`).dataset.order || 1)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('Tab title updated successfully!', 'success');

            // Update the displayed title
            document.getElementById(`tab-title-${tabId}`).textContent = newTitle;

            // Hide edit form and show content
            cancelTabEdit(tabId);
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Error updating tab title: ' + error, 'danger');
    });
}

function deleteTab(tabId) {
    if (!confirm('Are you sure you want to delete this tab? This action cannot be undone.')) {
        return;
    }

    fetch(`/api/guide/tabs/${tabId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('Tab deleted successfully!', 'success');
            setTimeout(() => {
                location.href = '/guide';
            }, 1000);
        } else {
            showAlert('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Error deleting tab: ' + error, 'danger');
    });
}

function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
    `;
    alertContainer.appendChild(alert);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
